@tailwind base;
@tailwind components;
@tailwind utilities;

::-webkit-scrollbar {
  display: none;
}

* {
  scrollbar-width: none;
}

html {
  -ms-overflow-style: none;
}
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.dot-flash::after {
  content: "...";
  animation: dot-blink 1.2s steps(3, end) infinite;
}

@keyframes dot-blink {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}

/* Optional: Custom scrollbar */
.custom-scroll::-webkit-scrollbar {
  width: 4px;
}
.custom-scroll::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}

/* Hide scrollbar for carousel */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dot-wave {
  display: flex;
  gap: 4px;
  align-items: center;
}
.dot-wave span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  display: inline-block;
  animation: wave 1.2s infinite ease-in-out both;
}
.dot-wave span:nth-child(1) {
  animation-delay: -0.24s;
}
.dot-wave span:nth-child(2) {
  animation-delay: -0.12s;
}
.dot-wave span:nth-child(3) {
  animation-delay: 0s;
}
.dot-wave {
  display: flex;
  align-items: center;
  height: 12px;
}

.dot-wave span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin-right: 3px;
  background: currentColor;
  animation: wave 1.3s linear infinite;
}

.dot-wave span:nth-child(2) {
  animation-delay: -1.1s;
}

.dot-wave span:nth-child(3) {
  animation-delay: -0.9s;
}

@keyframes wave {
  0%,
  60%,
  100% {
    transform: initial;
  }
  30% {
    transform: translateY(-4px);
  }
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}
@keyframes wave {
  0%,
  80%,
  100% {
    transform: scale(0.9) translateY(0);
  }
  40% {
    transform: scale(1.2) translateY(-5px);
  }
}
