import {
  motion,
  useInView,
} from "framer-motion";
import { useRef, useState } from "react";
import data from "../config/data.json";
import {
  Store,
  Smartphone,
  Apple,
  ExternalLink,
  Github,
  Code,
  ArrowUpRight,
  Sparkles,
  Zap,
  Layers,
  Globe,
  Database,
  Cpu,
  ChevronRight,
} from "lucide-react";

const iconMap = {
  Store: <Store className="w-5 h-5" />,
  Smartphone: <Smartphone className="w-5 h-5" />,
  Apple: <Apple className="w-5 h-5" />,
  Github: <Github className="w-5 h-5" />,
  Code: <Code className="w-5 h-5" />,
  ExternalLink: <ExternalLink className="w-5 h-5" />,
};

// Define a type for the valid icon keys
type IconKey = keyof typeof iconMap;

// Project type icons for better visual representation
const projectTypeIcons = {
  "Full Stack": <Layers className="w-6 h-6" />,
  "Backend": <Database className="w-6 h-6" />,
  "Mobile App": <Smartphone className="w-6 h-6" />,
  "Frontend": <Globe className="w-6 h-6" />,
  "AI/ML": <Cpu className="w-6 h-6" />,
};

// Color schemes for different project types
const projectColors = {
  "Full Stack": "from-blue-500 to-cyan-500",
  "Backend": "from-green-500 to-emerald-500",
  "Mobile App": "from-purple-500 to-pink-500",
  "Frontend": "from-orange-500 to-red-500",
  "AI/ML": "from-indigo-500 to-purple-500",
};

// Enhanced project card component
const ProjectCard = ({ project, index }: any) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, margin: "-100px" });

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 100,
      scale: 0.8,
      rotateX: -15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        delay: index * 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      y: -10,
      scale: 1.02,
      rotateX: 5,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const glowVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: [0, 0.5, 0],
      scale: [0.8, 1.2, 0.8],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const projectColor = projectColors[project.type as keyof typeof projectColors] || "from-gray-500 to-gray-600";

  return (
    <motion.div
      ref={cardRef}
      variants={cardVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      whileHover="hover"
      className="relative group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated glow effect */}
      <motion.div
        variants={glowVariants}
        initial="initial"
        animate={isHovered ? "animate" : "initial"}
        className={`absolute -inset-1 bg-gradient-to-r ${projectColor} rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300`}
      />

      {/* Main card */}
      <div className="relative bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl rounded-2xl overflow-hidden border border-gray-200/50 dark:border-gray-700/50 shadow-xl">
        {/* Header with animated background */}
        <div className="relative p-6 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20">
          <motion.div
            className={`absolute inset-0 bg-gradient-to-r ${projectColor} opacity-10`}
            animate={{
              backgroundPosition: isHovered ? "100% 0%" : "0% 0%",
            }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
            style={{ backgroundSize: "200% 100%" }}
          />

          <div className="relative flex items-start justify-between">
            <div className="flex items-center gap-4">
              {/* Animated project icon */}
              <motion.div
                className="relative"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <div className={`w-14 h-14 rounded-2xl bg-gradient-to-br ${projectColor} flex items-center justify-center text-white shadow-lg`}>
                  {projectTypeIcons[project.type as keyof typeof projectTypeIcons] || <Code className="w-6 h-6" />}
                </div>
                <motion.div
                  className={`absolute -inset-1 bg-gradient-to-br ${projectColor} rounded-2xl opacity-0`}
                  animate={{ opacity: isHovered ? 0.3 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                  {project.title}
                </h3>
                <div className="flex items-center gap-2">
                  <span className={`px-3 py-1 text-xs font-medium bg-gradient-to-r ${projectColor} text-white rounded-full`}>
                    {project.type}
                  </span>
                  {project.technologies && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {project.technologies}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Sparkle animation */}
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="text-yellow-400"
            >
              <Sparkles className="w-5 h-5" />
            </motion.div>
          </div>
        </div>

        {/* Card body */}
        <div className="p-6">
          {/* Technologies */}
          {project.technologies && (
            <div className="mb-6">
              <div className="flex flex-wrap gap-2">
                {project.technologies.split(", ").map((tech, i) => (
                  <motion.span
                    key={i}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: i * 0.1 }}
                    className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-700"
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          )}

          {/* Description with enhanced animations */}
          <div className="space-y-3 mb-6">
            {project.description.map((item, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: 0.1 * i,
                  duration: 0.5,
                  ease: "easeOut",
                }}
                className="flex items-start gap-3 text-gray-600 dark:text-gray-300"
              >
                <motion.div
                  className={`w-2 h-2 rounded-full bg-gradient-to-r ${projectColor} mt-2 flex-shrink-0`}
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: i * 0.2 }}
                />
                <span className="text-sm leading-relaxed">{item}</span>
              </motion.div>
            ))}
          </div>

          {/* Action buttons with enhanced styling */}
          <div className="flex flex-wrap gap-3">
            {project.link_1 && (
              <motion.a
                href={project.link_1}
                target="_blank"
                rel="noopener noreferrer"
                className={`group flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${projectColor} text-white rounded-xl text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {iconMap[project.icon_1 as IconKey] || iconMap.ExternalLink}
                <span>{project.icon_1_text}</span>
                <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </motion.a>
            )}

            {project.link_2 && (
              <motion.a
                href={project.link_2}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl text-sm font-medium transition-all duration-300 border border-gray-200 dark:border-gray-700"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {iconMap[project.icon_2 as IconKey] || iconMap.ExternalLink}
                <span>{project.icon_2_text}</span>
                <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </motion.a>
            )}
          </div>
        </div>

        {/* Animated footer */}
        <div className="relative h-2 overflow-hidden">
          <motion.div
            className={`absolute inset-0 bg-gradient-to-r ${projectColor}`}
            animate={{
              x: isHovered ? "0%" : "-100%",
            }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
          />
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800" />
        </div>
      </div>
    </motion.div>
  );
};

// Main Projects component
export const Projects = () => {
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const headerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  return (
    <section
      ref={containerRef}
      id="projects"
      className="relative py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 dark:bg-blue-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 dark:bg-purple-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-cyan-400/10 to-pink-400/10 rounded-full blur-3xl"
          animate={{
            rotate: 360,
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Enhanced header */}
        <motion.div
          variants={headerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium mb-4"
            whileHover={{ scale: 1.05 }}
          >
            <Zap className="w-4 h-4" />
            <span>Featured Work</span>
          </motion.div>

          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 dark:from-blue-400 dark:via-purple-400 dark:to-pink-400">
              My Projects
            </span>
          </h2>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            A showcase of innovative solutions I've built, from AI-powered applications
            to full-stack platforms that make a real impact.
          </motion.p>
        </motion.div>

        {/* Projects grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
        >
          {data.projects.map((project, index) => (
            <ProjectCard key={index} project={project} index={index} />
          ))}
        </motion.div>

        {/* Call to action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
        >
          <motion.a
            href="#contact"
            className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Let's Build Something Amazing</span>
            <ChevronRight className="w-5 h-5" />
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};
