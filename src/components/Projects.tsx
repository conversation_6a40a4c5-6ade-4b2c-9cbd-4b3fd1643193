import {
  motion,
  useInView,
  AnimatePresence,
} from "framer-motion";
import { useRef, useState, useEffect } from "react";
import data from "../config/data.json";
import {
  Store,
  Smartphone,
  Apple,
  ExternalLink,
  Github,
  Code,
  ArrowUpRight,
  Sparkles,
  Zap,
  Layers,
  Globe,
  Database,
  Cpu,
  ChevronRight,
  ChevronLeft,
  Play,
  Pause,
  Eye,
  Star,
} from "lucide-react";

const iconMap = {
  Store: <Store className="w-5 h-5" />,
  Smartphone: <Smartphone className="w-5 h-5" />,
  Apple: <Apple className="w-5 h-5" />,
  Github: <Github className="w-5 h-5" />,
  Code: <Code className="w-5 h-5" />,
  ExternalLink: <ExternalLink className="w-5 h-5" />,
};

// Define a type for the valid icon keys
type IconKey = keyof typeof iconMap;

// Project type icons for better visual representation
const projectTypeIcons = {
  "Full Stack": <Layers className="w-6 h-6" />,
  "Backend": <Database className="w-6 h-6" />,
  "Mobile App": <Smartphone className="w-6 h-6" />,
  "Frontend": <Globe className="w-6 h-6" />,
  "AI/ML": <Cpu className="w-6 h-6" />,
};

// Color schemes for different project types
const projectColors = {
  "Full Stack": "from-blue-500 to-cyan-500",
  "Backend": "from-green-500 to-emerald-500",
  "Mobile App": "from-purple-500 to-pink-500",
  "Frontend": "from-orange-500 to-red-500",
  "AI/ML": "from-indigo-500 to-purple-500",
};

// Enhanced project card component
const ProjectCard = ({ project, index, isFeatured = false }: any) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, margin: "-100px" });

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 100,
      scale: 0.8,
      rotateX: -15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        delay: index * 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      y: -10,
      scale: 1.02,
      rotateX: 5,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const glowVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: [0, 0.5, 0],
      scale: [0.8, 1.2, 0.8],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const projectColor = projectColors[project.type as keyof typeof projectColors] || "from-gray-500 to-gray-600";

  const cardClassName = isFeatured
    ? "w-full h-full"
    : "relative group cursor-pointer";

  return (
    <motion.div
      ref={cardRef}
      variants={cardVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      whileHover="hover"
      className={cardClassName}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated glow effect */}
      <motion.div
        variants={glowVariants}
        initial="initial"
        animate={isHovered ? "animate" : "initial"}
        className={`absolute -inset-1 bg-gradient-to-r ${projectColor} rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300`}
      />

      {/* Main card */}
      <div className={`relative bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl rounded-2xl overflow-hidden border border-gray-200/50 dark:border-gray-700/50 shadow-xl ${isFeatured ? 'h-full' : ''}`}>
        {/* Header with animated background */}
        <div className={`relative ${isFeatured ? 'p-8' : 'p-6'} bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20`}>
          <motion.div
            className={`absolute inset-0 bg-gradient-to-r ${projectColor} opacity-10`}
            animate={{
              backgroundPosition: isHovered ? "100% 0%" : "0% 0%",
            }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
            style={{ backgroundSize: "200% 100%" }}
          />

          <div className="relative flex items-start justify-between">
            <div className="flex items-center gap-4">
              {/* Animated project icon */}
              <motion.div
                className="relative"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <div className={`${isFeatured ? 'w-16 h-16' : 'w-14 h-14'} rounded-2xl bg-gradient-to-br ${projectColor} flex items-center justify-center text-white shadow-lg`}>
                  {projectTypeIcons[project.type as keyof typeof projectTypeIcons] || <Code className={`${isFeatured ? 'w-8 h-8' : 'w-6 h-6'}`} />}
                </div>
                <motion.div
                  className={`absolute -inset-1 bg-gradient-to-br ${projectColor} rounded-2xl opacity-0`}
                  animate={{ opacity: isHovered ? 0.3 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>

              <div>
                <h3 className={`${isFeatured ? 'text-3xl' : 'text-xl'} font-bold text-gray-900 dark:text-white mb-1`}>
                  {project.title}
                </h3>
                <div className="flex items-center gap-2">
                  <span className={`px-3 py-1 ${isFeatured ? 'text-sm' : 'text-xs'} font-medium bg-gradient-to-r ${projectColor} text-white rounded-full`}>
                    {project.type}
                  </span>
                  {project.technologies && (
                    <span className={`${isFeatured ? 'text-sm' : 'text-xs'} text-gray-500 dark:text-gray-400`}>
                      {project.technologies}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Sparkle animation */}
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="text-yellow-400"
            >
              <Sparkles className={`${isFeatured ? 'w-6 h-6' : 'w-5 h-5'}`} />
            </motion.div>
          </div>
        </div>

        {/* Card body */}
        <div className={`${isFeatured ? 'p-8' : 'p-6'} ${isFeatured ? 'flex-1' : ''}`}>
          {/* Technologies */}
          {project.technologies && (
            <div className="mb-6">
              <div className="flex flex-wrap gap-2">
                {project.technologies.split(", ").map((tech: string, i: number) => (
                  <motion.span
                    key={i}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: i * 0.1 }}
                    className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-700"
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          )}

          {/* Description with enhanced animations */}
          <div className="space-y-3 mb-6">
            {project.description.map((item: string, i: number) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: 0.1 * i,
                  duration: 0.5,
                  ease: "easeOut",
                }}
                className="flex items-start gap-3 text-gray-600 dark:text-gray-300"
              >
                <motion.div
                  className={`w-2 h-2 rounded-full bg-gradient-to-r ${projectColor} mt-2 flex-shrink-0`}
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: i * 0.2 }}
                />
                <span className="text-sm leading-relaxed">{item}</span>
              </motion.div>
            ))}
          </div>

          {/* Action buttons with enhanced styling */}
          <div className="flex flex-wrap gap-3">
            {project.link_1 && (
              <motion.a
                href={project.link_1}
                target="_blank"
                rel="noopener noreferrer"
                className={`group flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${projectColor} text-white rounded-xl text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {iconMap[project.icon_1 as IconKey] || iconMap.ExternalLink}
                <span>{project.icon_1_text}</span>
                <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </motion.a>
            )}

            {project.link_2 && (
              <motion.a
                href={project.link_2}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl text-sm font-medium transition-all duration-300 border border-gray-200 dark:border-gray-700"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {iconMap[project.icon_2 as IconKey] || iconMap.ExternalLink}
                <span>{project.icon_2_text}</span>
                <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </motion.a>
            )}
          </div>
        </div>

        {/* Animated footer */}
        <div className="relative h-2 overflow-hidden">
          <motion.div
            className={`absolute inset-0 bg-gradient-to-r ${projectColor}`}
            animate={{
              x: isHovered ? "0%" : "-100%",
            }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
          />
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800" />
        </div>
      </div>
    </motion.div>
  );
};

// Main Projects component with carousel design
export const Projects = () => {
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState("All");

  const projects = data.projects;
  const categories = ["All", ...Array.from(new Set(projects.map(p => p.type)))];

  const filteredProjects = selectedCategory === "All"
    ? projects
    : projects.filter(p => p.type === selectedCategory);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % filteredProjects.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, filteredProjects.length]);

  const nextProject = () => {
    setCurrentIndex((prev) => (prev + 1) % filteredProjects.length);
  };

  const prevProject = () => {
    setCurrentIndex((prev) => (prev - 1 + filteredProjects.length) % filteredProjects.length);
  };

  const goToProject = (index: number) => {
    setCurrentIndex(index);
  };

  const headerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  return (
    <section
      ref={containerRef}
      id="projects"
      className="relative py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 dark:bg-blue-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 dark:bg-purple-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Enhanced header */}
        <motion.div
          variants={headerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-12"
        >
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium mb-4"
            whileHover={{ scale: 1.05 }}
          >
            <Zap className="w-4 h-4" />
            <span>Featured Work</span>
          </motion.div>

          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 dark:from-blue-400 dark:via-purple-400 dark:to-pink-400">
              My Projects
            </span>
          </h2>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            A showcase of innovative solutions I've built, from AI-powered applications
            to full-stack platforms that make a real impact.
          </motion.p>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            {categories.map((category) => (
              <motion.button
                key={category}
                onClick={() => {
                  setSelectedCategory(category);
                  setCurrentIndex(0);
                }}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                    : "bg-white/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {category}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Main Carousel Container */}
        <div className="relative">
          {/* Featured Project Display */}
          <div className="relative h-[600px] mb-8 overflow-hidden rounded-3xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="absolute inset-0"
              >
                <ProjectCard
                  project={filteredProjects[currentIndex]}
                  index={currentIndex}
                  isFeatured={true}
                />
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <motion.button
                onClick={prevProject}
                className="p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeft className="w-6 h-6 text-gray-600 dark:text-gray-300" />
              </motion.button>

              <motion.button
                onClick={nextProject}
                className="p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronRight className="w-6 h-6 text-gray-600 dark:text-gray-300" />
              </motion.button>

              <motion.button
                onClick={() => setIsAutoPlaying(!isAutoPlaying)}
                className="p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                {isAutoPlaying ? (
                  <Pause className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                ) : (
                  <Play className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                )}
              </motion.button>
            </div>

            {/* Project Counter */}
            <div className="flex items-center gap-2 px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-full">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {currentIndex + 1} / {filteredProjects.length}
              </span>
            </div>
          </div>

          {/* Thumbnail Navigation */}
          <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={index}
                onClick={() => goToProject(index)}
                className={`flex-shrink-0 w-64 h-32 rounded-xl cursor-pointer transition-all duration-300 ${
                  index === currentIndex
                    ? "ring-4 ring-blue-500 ring-opacity-50 scale-105"
                    : "hover:scale-102 opacity-70 hover:opacity-100"
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="w-full h-full bg-gradient-to-br from-white/90 to-gray-100/90 dark:from-gray-800/90 dark:to-gray-900/90 backdrop-blur-lg rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${projectColors[project.type as keyof typeof projectColors] || "from-gray-500 to-gray-600"} flex items-center justify-center text-white text-xs`}>
                      {projectTypeIcons[project.type as keyof typeof projectTypeIcons] || <Code className="w-4 h-4" />}
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                        {project.title}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {project.type}
                      </p>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">
                    {project.description[0]}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Progress Indicators */}
          <div className="flex justify-center gap-2 mt-8">
            {filteredProjects.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => goToProject(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 scale-125"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              />
            ))}
          </div>
        </div>

        {/* Call to action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
        >
          <motion.a
            href="#contact"
            className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Let's Build Something Amazing</span>
            <ChevronRight className="w-5 h-5" />
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};
