{"name": "<PERSON><PERSON><PERSON>", "date_of_birth": "05.01.1999", "titles": ["Software Engineer", "Full Stack Developer", "Flutter Developer", "Professional Coder", "AI-powered Developer"], "location": "Madurai, India", "experience_in_years": "3+ years", "about": "Full Stack Software Engineer with 3 years of experience creating web and mobile products that make a difference. Skilled in React, Node.js, Next.js, and Flutter, with a knack for integrating the latest AI tools to automate and simplify complex workflows. Experienced in building AI agents and developing multi-channel platforms (MCP) to boost efficiency and scale. Known for delivering production-ready features, mentoring teammates, and bringing energy and attention to detail to every project.", "roles": [{"title": "Full Stack Web Development", "description": "Building scalable and performant web applications using React, Remix, and Node.js."}, {"title": "Mobile App Development", "description": "Developing cross-platform mobile apps for Android and iOS using Flutter."}, {"title": "macOS App Development", "description": "Creating macOS applications with a seamless native experience."}, {"title": "Backend & API Development", "description": "Designing robust backend systems and REST/GraphQL APIs using Node.js, Python(Fast API) & Supabase."}, {"title": "LLM & OpenAI Logic", "description": "Integrating AI-powered solutions, chat completion, and automation using OpenAI APIs."}, {"title": "AI Agents Development", "description": "Building autonomous and task-driven AI agents using OpenAI, LangChain, and vector databases for intelligent workflows."}], "experience": [{"company": "BINARYCHAKRA", "role": "Software Engineer", "period": "Oct 2023 - Present", "current_working": true, "current_salary": "500,000 CTC", "location": "Madurai", "achievements": ["Developed and deployed multiple successful Shopify apps using Polaris, Remix, React, and Node.js, enhancing client satisfaction and user engagement.", "Built cross-platform macOS applications with Flutter, reducing development time and maintenance efforts.", "Designed and optimized real-time data systems using MQTT protocols, improving application performance by 30%.", "Developed and optimized API logic using Python FastAPI to enhance performance and integrate AI models seamlessly.", "Collaborated with cross-functional teams to integrate APIs, third-party services, and cloud solutions."]}, {"company": "CRON24 TECHNOLOGIES", "role": "Mobile App Developer", "current_working": false, "period": "Mar 2022 – Sept 2023", "location": "Madurai", "achievements": ["Designed and implemented efficient state management systems for Android & iOS applications, ensuring smooth functionality.", "Developed and maintained mobile apps using Dart, Flutter, and integrated APIs with JSON for dynamic data handling.", "Integrated key features like Google Maps API, Stripe payments, Firebase push notifications, Firebase Realtime Database, Google Analytics, and Google Ads", "Managed app performance, debugging, and deployment.", "Handled multilingual content updates to improve localization and user engagement."]}], "technical_skills": {"languages": ["JavaScript", "Dart", "Python (FastAPI)", "HTML", "CSS"], "frameworks": ["Flutter", "React", "Remix", "Express"], "apis": ["REST API", "GraphQL", "Google Maps API", "JSON"], "platforms": ["Shopify Polaris", "AWS S3", "AWS Lambda", "AWS API Gateway", "Firebase", "Git"], "databases": ["Supabase", "MongoDB", "MySQL", "Firebase Realtime Database"], "ai_integration": ["OpenAI Chat Completion", "Azure OpenAI", "Hugging Face"], "messaging": ["Kafka", "MQTT"]}, "projects": [{"title": "STORE BLOG", "type": "Full Stack", "link_1": "https://apps.shopify.com/storeblog-boost-your-store-seo", "icon_1": "Store", "icon_1_text": "Shopify App Store", "description": ["AI-powered Shopify app for SEO-optimized blog articles", "OpenAI GPT integration with custom prompt engineering", "AWS Cron for automated publishing with multi-language support", "Hugging Face models for AI-generated images"]}, {"title": "AI Image Generation API", "type": "Backend", "description": ["Developed a FastAPI backend for AI-powered image generation", "Handled prompt processing and image creation using Hugging Face models", "Optimized API performance and response time for high scalability"]}, {"title": "AI-Powered Notification App", "type": "Full Stack", "description": ["Shopify-based app that generates AI-driven notifications to boost store sales", "Uses OpenAI to analyze store data and suggest useful notifications", "Kafka-based pub-sub architecture for real-time event processing", "Sends notifications to merchants based on triggered webhooks"]}, {"title": "FILMPLACE", "type": "Mobile App", "technologies": "Flutter, Firebase, Stripe, Google Maps", "link_1": "https://play.google.com/store/apps/details?id=com.filmplace.app&pcampaignid=web_share", "link_2": "https://apps.apple.com/in/app/filmplace-film-locations/id1597538864", "icon_1": "Smartphone", "icon_2": "Apple", "icon_1_text": "Android", "icon_2_text": "App Store", "description": ["Location booking platform for filmmakers", "Stripe payments with 15% error reduction", "Firebase integration with 30% engagement increase", "Multi-platform social login implementation"]}, {"title": "CABBY", "type": "Mobile App", "technologies": "Flutter, Firebase, GeoLocator", "link_1": "https://play.google.com/store/apps/details?id=com.cron24.cabby&hl=en-IN", "link_2": "https://play.google.com/store/apps/details?id=com.cron24.cabbydriver&hl=en-IN", "icon_1": "Smartphone", "icon_2": "Smartphone", "icon_1_text": "Android", "icon_2_text": "Android", "description": ["Full-featured ride-hailing platform", "Real-time GPS tracking with 30% better ETA", "Background services with 10% crash reduction", "Multi-language support for global reach"]}], "education": [{"school": "K.L.N. COLLEGE OF ENGINEERING", "degree": "Master of Computer Applications", "period": "April 2022", "location": "Madurai", "grade": "CGPA: 9.1 / 10"}, {"school": "SOURASHTRA COLLEGE", "degree": "B.Sc. in Computer Science", "period": "April 2020", "location": "Madurai", "grade": "CGPA: 7.1 / 10"}], "contact": {"email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "linkedin": "https://www.linkedin.com/in/harshavardhannj/", "portfolio_url": "https://harsha-pro.vercel.app/", "github": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "phone": "91+9976871783", "resume": {"download": "../assets/Harsha-Resume.pdf", "view": "../assets/Harsha-Resume.pdf"}}, "expected_salary": " 1,000,000 CTC", "hobbies": ["Traveling", "Trying new food", "Watching movies", "Playing games", "music"], "strengths": [{"strength": "Problem-solving", "description": "I’m really good at tackling problems head-on. I enjoy finding creative solutions, especially when things aren’t going as planned. For instance, in my last project, I identified a technical roadblock and came up with a solution that sped up the whole process."}, {"strength": "Communication", "description": "I pride myself on being a good communicator. I make sure everyone is on the same page, whether that’s leading team meetings or just keeping in touch with colleagues. This way, we avoid misunderstandings and keep things running smoothly."}, {"strength": "Attention to Detail", "description": "I’m someone who really cares about the small stuff. I always double-check my work to make sure everything’s in place, especially when it comes to delivering high-quality results. I think it’s the little things that make a big difference."}], "weaknesses": [{"weakness": "Delegation", "description": "I tend to take on a lot of tasks myself because I want to make sure everything’s done right. But I’m working on learning to trust my team more, setting clear expectations, and giving them the freedom to take the lead on certain things."}, {"weakness": "Asking for Help", "description": "I sometimes struggle with asking for help when I’m stuck. I guess I don’t want to bother anyone. But I’ve started recognizing when I need assistance and I’ve been making an effort to reach out and collaborate more."}, {"weakness": "Work-life Balance", "description": "Balancing work and personal life can be a challenge for me. I get really into what I’m doing and sometimes forget to take a step back. To fix this, I’ve started using tools to help me better organize my time and make sure I’m not overworking."}]}